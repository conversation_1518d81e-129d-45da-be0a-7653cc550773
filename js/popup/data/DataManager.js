/**
 * Data Manager
 * Handles all storage operations, history management, and data persistence
 */
import { BaseManager } from '../core/BaseManager.js';

export class DataManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
        // Check for pending analysis and context menu actions on initialization
        // await this.checkPendingAnalysis(); // DEACTIVATED - Handled by message listener
        // await this.checkContextMenuActions(); // DEACTIVATED - Handled by message listener
    }

    /**
     * Check for pending analysis from content script
     */
    async checkPendingAnalysis() {
        try {
            const result = await chrome.storage.local.get(['pendingAnalysis']);
            if (result.pendingAnalysis) {
                const { type, data, timestamp } = result.pendingAnalysis;

                // Only process if it's recent (within 10 seconds)
                if (Date.now() - timestamp < 10000) {
                    console.log('Found pending analysis:', type, data.substring(0, 50) + '...');

                    // Clear the pending analysis
                    await chrome.storage.local.remove(['pendingAnalysis']);

                    // Trigger the appropriate analysis
                    if (type === 'selection' && data) {
                        await this.controller.analysisManager.analyzeSelectionWithText(data);
                    }
                } else {
                    // Clear old pending analysis
                    await chrome.storage.local.remove(['pendingAnalysis']);
                }
            }
        } catch (error) {
            console.error('Error checking pending analysis:', error);
        }
    }

    /**
     * Check for context menu actions
     */
    async checkContextMenuActions() {
        try {
            const result = await chrome.storage.local.get([
                'showUpgradeSection',
                'showCustomAnalysis',
                'showHistorySection',
                'contextMenuData',
                'upgradeReason',
                'contextMenuTrigger'
            ]);

            // Handle upgrade section request
            if (result.showUpgradeSection) {
                await chrome.storage.local.remove(['showUpgradeSection', 'upgradeReason']);
                // Navigate to upgrade section without tracking (context menu action is a fresh start)
                this.controller.navigateToSection('upgradeSection', false);
                return;
            }

            // Handle custom analysis request
            if (result.showCustomAnalysis) {
                await chrome.storage.local.remove(['showCustomAnalysis', 'contextMenuData']);
                // Navigate to custom form without tracking (context menu action is a fresh start)
                this.controller.navigateToSection('customForm', false);

                // If there's context data, populate the form
                if (result.contextMenuData && result.contextMenuData.selectedText) {
                    const customPrompt = document.getElementById('customPrompt');
                    if (customPrompt) {
                        customPrompt.value = `Please analyze the following selected text:\n\n${result.contextMenuData.selectedText}`;
                    }
                }
                return;
            }

            // Handle history section request
            if (result.showHistorySection) {
                await chrome.storage.local.remove(['showHistorySection']);
                // Navigate to history section without tracking (context menu action is a fresh start)
                this.controller.navigateToSection('analysisHistorySection', false);
                this.loadAndDisplayAnalysis();
                return;
            }

            // Handle context menu trigger (new format)
            if (result.contextMenuTrigger) {
                const triggerAge = Date.now() - result.contextMenuTrigger.timestamp;

                // Only process if recent (within 30 seconds)
                if (triggerAge < 30000) {
                    await chrome.storage.local.remove(['contextMenuTrigger']);

                    // Show loading section immediately for user feedback
                    this.controller.uiManager.showSection('loadingSection');

                    if (result.contextMenuTrigger.type === 'selection') {
                        // Trigger text analysis with loading animation
                        await this.controller.analysisManager.analyzeSelectionWithText(result.contextMenuTrigger.data);
                    } else if (result.contextMenuTrigger.type === 'page') {
                        // Trigger page analysis with loading animation
                        await this.controller.analysisManager.analyzePageWithData(result.contextMenuTrigger.data);
                    }
                    return;
                }
            }

        } catch (error) {
            console.error('Error checking context menu actions:', error);
        }
    }

    /**
     * Load API key from storage
     */
    async loadApiKey() {
        try {
            const result = await chrome.storage.sync.get(['agentHustleApiKey']);
            this.controller.apiKey = result.agentHustleApiKey;
        } catch (error) {
            console.error('Error loading API key:', error);
        }
    }

    /**
     * Save API key to storage
     */
    async saveApiKey(apiKey) {
        try {
            await chrome.storage.sync.set({ agentHustleApiKey: apiKey });
            this.controller.apiKey = apiKey;
            this.controller.uiManager.updateApiStatus();
            this.controller.uiManager.updateUI();
        } catch (error) {
            console.error('Error saving API key:', error);
            this.controller.uiManager.showError('Failed to save API key');
        }
    }

    /**
     * Save analysis to history
     */
    async saveAnalysis(analysisType, result) {
        try {
            const newEntry = {
                id: `analysis-${Date.now()}`,
                date: new Date().toISOString(),
                analysisType,
                result
            };

            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            history.push(newEntry);

            await chrome.storage.local.set({ hustleplugAnalysis: history });
        } catch (error) {
            console.error('Error saving analysis:', error);
        }
    }

    /**
     * Save chat session to history
     */
    async saveChatSession(sessionData) {
        try {
            // Ensure session has all required fields
            const sessionToSave = {
                id: sessionData.id || `chat-${Date.now()}`,
                createdAt: sessionData.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                messages: sessionData.messages || [],
                title: sessionData.title || 'New Chat',
                status: sessionData.status || 'active',
                messageCount: sessionData.messages ? sessionData.messages.length : 0,
                ...sessionData
            };

            const data = await chrome.storage.local.get(['agent_hustle_chat_history']);
            const history = data.agent_hustle_chat_history || [];
            
            // Find existing session or add new one
            const existingIndex = history.findIndex(s => s.id === sessionToSave.id);
            if (existingIndex !== -1) {
                history[existingIndex] = sessionToSave;
            } else {
                history.unshift(sessionToSave);
            }

            await chrome.storage.local.set({ agent_hustle_chat_history: history });
            return true;
        } catch (error) {
            console.error('Error saving chat session:', error);
            return false;
        }
    }

    /**
     * Load and display analysis history with pagination
     */
    async loadAndDisplayAnalysis() {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const allHistory = data.hustleplugAnalysis || [];
            const historyList = document.getElementById('analysisHistoryList');
            const paginationContainer = document.getElementById('historyPagination');

            // Clear existing content to prevent duplicates
            if (historyList) {
                historyList.innerHTML = '';
            }
            if (paginationContainer) {
                paginationContainer.innerHTML = '';
            }

            if (allHistory.length === 0) {
                historyList.innerHTML = '<p style="text-align: center; color: #888;">No analysis history found.</p>';
                return;
            }

            // Sort by date (newest first)
            const sortedHistory = allHistory.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            // Get paginated data
            const paginatedData = this.controller.historyPagination.getPaginatedData(sortedHistory);
            const paginatedHistory = paginatedData.data;
            const pagination = paginatedData.pagination;
            
            // Display paginated items
            paginatedHistory.forEach(item => {
                const itemEl = document.createElement('div');
                itemEl.className = 'history-item';
                itemEl.dataset.analysisId = item.id; // Add ID to dataset

                // Handle both string and object based results for backward compatibility
                const resultContent = typeof item.result === 'object' && item.result !== null && item.result.content ? item.result.content : item.result;
                const preview = this.controller.uiManager.formatAnalysisContent(resultContent, true);
                
                // Create buttons with SVG icons like the original
                const telegramButtonHtml = `
                    <button class="telegram-send-btn" data-analysis-id="${item.id}" title="Send to Telegram">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 3L3 10.5L10.5 13.5L13.5 21L21 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.5 13.5L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                `;

                const discordButtonHtml = `
                    <button class="discord-send-btn" data-analysis-id="${item.id}" title="Send to Discord">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                        </svg>
                    </button>
                `;

                itemEl.innerHTML = `
                    <div class="history-item-header">
                        <span class="history-item-title">${item.analysisType}</span>
                        <div class="history-item-meta">
                            <span class="history-item-date">${new Date(item.date).toLocaleString()}</span>
                            <div class="history-item-actions">
                                ${telegramButtonHtml}
                                ${discordButtonHtml}
                                <button class="delete-btn" data-analysis-id="${item.id}" title="Delete analysis">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M10 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M14 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="history-item-summary">
                        ${preview}
                    </div>
                    <div class="history-item-footer">
                        <span class="view-details-link" data-analysis-id="${item.id}">View Details &rarr;</span>
                    </div>
                `;
                
                historyList.appendChild(itemEl);
            });

            // Update pagination controls
            if (paginationContainer) {
                paginationContainer.innerHTML = this.controller.historyPagination.generatePaginationHTML(pagination);
            }
            
        } catch (error) {
            console.error('Error loading analysis history:', error);
            const historyList = document.getElementById('analysisHistoryList');
            if (historyList) {
                historyList.innerHTML = '<p style="text-align: center; color: #f44;">Error loading history.</p>';
            }
        }
    }

    /**
     * Load and display chat history with pagination
     */
    async loadAndDisplayChatHistory() {
        try {
            const data = await chrome.storage.local.get(['agent_hustle_chat_history']);
            const allHistory = data.agent_hustle_chat_history || [];
            const historyList = document.getElementById('chatHistoryList');
            const paginationContainer = document.getElementById('chatHistoryPagination');

            // Clear existing content to prevent duplicates
            if (historyList) {
                historyList.innerHTML = '';
            }
            if (paginationContainer) {
                paginationContainer.innerHTML = '';
            }

            if (allHistory.length === 0) {
                historyList.innerHTML = '<p style="text-align: center; color: #888;">No chat history found.</p>';
                return;
            }

            // Sort by date (newest first)
            const sortedHistory = allHistory.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
            
            // Get paginated data
            const paginatedData = this.controller.historyPagination.getPaginatedData(sortedHistory);
            const paginatedHistory = paginatedData.data;
            const pagination = paginatedData.pagination;
            
            // Display paginated items
            paginatedHistory.forEach(session => {
                const itemEl = document.createElement('div');
                itemEl.className = 'history-item';
                itemEl.dataset.chatId = session.id;

                // Create formatted content for chat session
                const lastMessage = session.messages && session.messages.length > 0 
                    ? session.messages[session.messages.length - 1].content 
                    : 'No messages';
                const preview = this.controller.uiManager.formatAnalysisContent(lastMessage, true);
                
                // Create buttons for chat sessions
                const resumeButtonHtml = `
                    <button class="resume-chat-btn" data-chat-id="${session.id}" title="Resume Chat">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 12L12 8L16 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 8V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                `;

                const telegramButtonHtml = `
                    <button class="telegram-send-btn" data-chat-id="${session.id}" title="Send to Telegram">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 3L3 10.5L10.5 13.5L13.5 21L21 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.5 13.5L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                `;

                const discordButtonHtml = `
                    <button class="discord-send-btn" data-chat-id="${session.id}" title="Send to Discord">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                        </svg>
                    </button>
                `;

                itemEl.innerHTML = `
                    <div class="history-item-header">
                        <span class="history-item-title">💬 ${session.title}</span>
                        <div class="history-item-meta">
                            <span class="history-item-date">${new Date(session.updatedAt).toLocaleString()}</span>
                            <span class="history-item-count">${session.messageCount} messages</span>
                            <div class="history-item-actions">
                                ${resumeButtonHtml}
                                ${telegramButtonHtml}
                                ${discordButtonHtml}
                                <button class="delete-btn" data-chat-id="${session.id}" title="Delete chat">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M10 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M14 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="history-item-summary">
                        ${preview}
                    </div>
                    <div class="history-item-footer">
                        <span class="view-details-link" data-chat-id="${session.id}">View Details &rarr;</span>
                    </div>
                `;
                
                historyList.appendChild(itemEl);
            });

            // Update pagination controls
            if (paginationContainer) {
                paginationContainer.innerHTML = this.controller.historyPagination.generatePaginationHTML(pagination);
            }
            
        } catch (error) {
            console.error('Error loading chat history:', error);
            const historyList = document.getElementById('chatHistoryList');
            if (historyList) {
                historyList.innerHTML = '<p style="text-align: center; color: #f44;">Error loading chat history.</p>';
            }
        }
    }

    /**
     * View a specific analysis from history
     */
    async viewAnalysisFromHistory(analysisId) {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            const analysisItem = history.find(item => item.id === analysisId);

            if (analysisItem) {
                this.controller.analysisManager.displayResults(
                    analysisItem.result,
                    analysisItem.analysisType,
                    new Date(analysisItem.date)
                );
                // Use navigation context to track where we came from
                this.controller.navigateToSection('resultsSection');
            } else {
                this.controller.uiManager.showError('Could not find the selected analysis.');
            }
        } catch (error) {
            console.error('Error viewing analysis from history:', error);
            this.controller.uiManager.showError('Failed to load the analysis.');
        }
    }

    /**
     * Delete analysis from history
     */
    async deleteAnalysis(analysisId) {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            
            // Filter out the analysis to delete
            const updatedHistory = history.filter(item => item.id !== analysisId);
            
            // Update storage
            await chrome.storage.local.set({ hustleplugAnalysis: updatedHistory });
            
            // Check if current page is now empty and adjust if needed
            const currentPageData = this.controller.historyPagination.getPaginatedData(updatedHistory);
            if (currentPageData.data.length === 0 && this.controller.historyPagination.currentPage > 1) {
                this.controller.historyPagination.currentPage = Math.max(1, this.controller.historyPagination.currentPage - 1);
            }
            
            // Refresh the display
            await this.loadAndDisplayAnalysis();
            
            this.controller.uiManager.showSuccess('Analysis deleted successfully');
        } catch (error) {
            console.error('Error deleting analysis:', error);
            this.controller.uiManager.showError('Failed to delete analysis');
        }
    }

    /**
     * Get analysis by ID
     */
    async getAnalysisById(analysisId) {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            return history.find(item => item.id === analysisId);
        } catch (error) {
            console.error('Error getting analysis by ID:', error);
            return null;
        }
    }

    /**
     * Clear all analysis history
     */
    async clearAllHistory() {
        try {
            await chrome.storage.local.set({ hustleplugAnalysis: [] });
            await this.loadAndDisplayAnalysis();
            this.controller.uiManager.showSuccess('All analysis history cleared');
        } catch (error) {
            console.error('Error clearing history:', error);
            this.controller.uiManager.showError('Failed to clear history');
        }
    }

    /**
     * Export all analysis history
     */
    async exportAllHistory() {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            
            if (history.length === 0) {
                this.controller.uiManager.showError('No history to export');
                return;
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                totalAnalyses: history.length,
                analyses: history
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-history-${Date.now()}.json`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.controller.uiManager.showSuccess('History exported successfully!');
        } catch (error) {
            console.error('Error exporting history:', error);
            this.controller.uiManager.showError('Failed to export history');
        }
    }
}
