/**
 * Popup Controller
 * Central coordinator for all popup managers
 */
import { BaseManager } from './BaseManager.js';
import { checkProStatus } from '../../user/proStatus.js';
import { ChatManager } from '../chat/ChatManager.js';

export class PopupController extends BaseManager {
    constructor() {
        super(null); // Controller doesn't have a parent controller
        this.managers = new Map();
        this.initialized = false;
        
        // Core properties from original AgentHustleAnalyzer
        this.baseUrl = null;
        this.apiKey = null;
        this.sessionId = null;
        this.currentAnalysis = null;
        this.currentEditingPrompt = null;
        this.currentFilterTag = null;
        
        // Navigation context for back buttons
        this.navigationStack = [];
        this.currentSection = null;
        
        // Pagination managers will be set by main analyzer
        this.historyPagination = null;
        this.promptPagination = null;
    }

    /**
     * Register a manager
     */
    registerManager(name, manager) {
        if (!(manager instanceof BaseManager)) {
            throw new Error(`Manager ${name} must extend BaseManager`);
        }
        this.managers.set(name, manager);
    }

    /**
     * Get a manager by name
     */
    getManager(name) {
        const manager = this.managers.get(name);
        if (!manager) {
            throw new Error(`Manager ${name} not found`);
        }
        return manager;
    }

    /**
     * Initialize all managers in proper order
     */
    async init() {
        try {
            // Initialize managers in dependency order
            const initOrder = [
                'dataManager',
                'uiManager', 
                'settingsManager',
                'analysisManager',
                'promptUIManager',
                'integrationManager',
                'autoSendManager',
                'chatManager',
                'eventManager' // Last, as it depends on others
            ];

            for (const managerName of initOrder) {
                const manager = this.managers.get(managerName);
                if (manager) {
                    await manager.init();
                    console.log(`${managerName} initialized`);
                }
            }

            this.initialized = true;
            console.log('PopupController initialized successfully');
        } catch (error) {
            console.error('PopupController initialization failed:', error);
            throw error;
        }
    }

    /**
     * Check if controller is initialized
     */
    ensureInitialized() {
        if (!this.initialized) {
            throw new Error('PopupController not initialized');
        }
    }

    /**
     * Cleanup all managers
     */
    cleanup() {
        for (const [name, manager] of this.managers) {
            try {
                manager.cleanup();
            } catch (error) {
                console.error(`Error cleaning up ${name}:`, error);
            }
        }
        this.managers.clear();
        this.initialized = false;
    }

    // Convenience getters for commonly used managers
    get uiManager() {
        return this.getManager('uiManager');
    }

    get eventManager() {
        return this.getManager('eventManager');
    }

    get analysisManager() {
        return this.getManager('analysisManager');
    }

    get settingsManager() {
        return this.getManager('settingsManager');
    }

    get dataManager() {
        return this.getManager('dataManager');
    }

    get promptUIManager() {
        return this.getManager('promptUIManager');
    }

    get integrationManager() {
        return this.getManager('integrationManager');
    }

    get autoSendManager() {
        return this.getManager('autoSendManager');
    }

    get chatManager() {
        return this.getManager('chatManager');
    }

    /**
     * Navigation context management
     */
    
    /**
     * Navigate to a section and track the previous section
     */
    navigateToSection(sectionId, trackNavigation = true) {
        console.log(`🧭 Navigating to: ${sectionId} (from: ${this.currentSection})`);
        
        if (trackNavigation && this.currentSection && this.currentSection !== sectionId) {
            // Don't add the same section to the stack consecutively
            if (this.navigationStack.length === 0 || this.navigationStack[this.navigationStack.length - 1] !== this.currentSection) {
                this.navigationStack.push(this.currentSection);
                console.log(`📚 Navigation stack: [${this.navigationStack.join(' -> ')}]`);
            }
        }
        
        this.currentSection = sectionId;
        this.uiManager.showSection(sectionId);
    }
    
    /**
     * Go back to the previous section
     */
    goBack() {
        console.log(`🔙 Going back. Current: ${this.currentSection}, Stack: [${this.navigationStack.join(' -> ')}]`);
        
        if (this.navigationStack.length > 0) {
            const previousSection = this.navigationStack.pop();
            console.log(`🔙 Returning to: ${previousSection}`);
            this.currentSection = previousSection;
            this.uiManager.showSection(previousSection);
            return previousSection;
        } else {
            // Default fallback to actions section
            console.log(`🔙 No navigation history, defaulting to: actionsSection`);
            this.currentSection = 'actionsSection';
            this.uiManager.showSection('actionsSection');
            return 'actionsSection';
        }
    }
    
    /**
     * Clear navigation stack
     */
    clearNavigationStack() {
        this.navigationStack = [];
    }
    
    /**
     * Get the previous section without navigating
     */
    getPreviousSection() {
        return this.navigationStack.length > 0 ? this.navigationStack[this.navigationStack.length - 1] : 'actionsSection';
    }

    /**
     * Check pro status - convenience method for managers
     */
    async checkProStatus() {
        return await checkProStatus();
    }
}
